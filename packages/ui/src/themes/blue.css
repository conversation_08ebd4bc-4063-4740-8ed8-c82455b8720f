/* Blue theme - Light mode */
[data-theme='blue'] {
  --color-background: oklch(0.98 0.02 240);
  --color-foreground: oklch(0.15 0.08 240);
  --color-primary: oklch(0.5 0.2 240);
  --color-primary-foreground: oklch(0.98 0.02 240);
  --color-secondary: oklch(0.92 0.05 240);
  --color-secondary-foreground: oklch(0.35 0.12 240);
  --color-muted: oklch(0.94 0.03 240);
  --color-muted-foreground: oklch(0.45 0.1 240);
  --color-accent: oklch(0.94 0.03 240);
  --color-accent-foreground: oklch(0.15 0.08 240);
  --color-destructive: oklch(0.541 0.229 27.422);
  --color-destructive-foreground: oklch(0.985 0 0);
  --color-border: oklch(0.87 0.04 240);
  --color-input: oklch(0.96 0.03 240);
  --color-ring: oklch(0.5 0.2 240);
  --color-card: oklch(0.98 0.02 240);
  --color-card-foreground: oklch(0.15 0.08 240);
  --color-popover: oklch(0.98 0.02 240);
  --color-popover-foreground: oklch(0.15 0.08 240);
}

/* Blue theme - Dark mode */
[data-theme='blue'][data-mode='dark'] {
  --color-background: oklch(0.15 0.08 240);
  --color-foreground: oklch(0.98 0.02 240);
  --color-primary: oklch(0.7 0.15 240);
  --color-primary-foreground: oklch(0.15 0.08 240);
  --color-secondary: oklch(0.25 0.1 240);
  --color-secondary-foreground: oklch(0.75 0.08 240);
  --color-muted: oklch(0.3 0.06 240);
  --color-muted-foreground: oklch(0.65 0.05 240);
  --color-accent: oklch(0.3 0.06 240);
  --color-accent-foreground: oklch(0.98 0.02 240);
  --color-destructive: oklch(0.396 0.141 25.723);
  --color-destructive-foreground: oklch(0.985 0 0);
  --color-border: oklch(0.35 0.06 240);
  --color-input: oklch(0.25 0.1 240);
  --color-ring: oklch(0.7 0.15 240);
  --color-card: oklch(0.15 0.08 240);
  --color-card-foreground: oklch(0.98 0.02 240);
  --color-popover: oklch(0.15 0.08 240);
  --color-popover-foreground: oklch(0.98 0.02 240);
}