import type { ThemeConfig } from "../types/theme";

// Constants for theme class patterns
const THEME_CLASS_PREFIXES = ["theme-", "dark", "light"] as const;

/**
 * Tracks if this is the first theme application to prevent flash
 */
let isInitialThemeApplication = true;

/**
 * Determines if the current theme should be dark mode
 */
const shouldUseDarkMode = (config: ThemeConfig): boolean => {
  if (config.mode === "dark") return true;
  if (config.mode === "light") return false;

  // mode === "auto"
  return (
    typeof window !== "undefined" &&
    window.matchMedia("(prefers-color-scheme: dark)").matches
  );
};

/**
 * Removes all existing theme-related classes from the document root
 */
const clearThemeClasses = (root: HTMLElement): void => {
  const existingClasses = Array.from(root.classList).filter((cls) =>
    THEME_CLASS_PREFIXES.some((prefix) => cls.startsWith(prefix)),
  );

  if (existingClasses.length > 0) {
    root.classList.remove(...existingClasses);
  }
};

/**
 * Applies the current theme configuration to the document
 */
export function applyTheme(config: ThemeConfig): void {
  if (typeof document === "undefined") return;

  const root = document.documentElement;

  // Disable transitions on first load to prevent flash
  if (isInitialThemeApplication) {
    root.classList.add("no-transition");
    isInitialThemeApplication = false;
  }

  // Clear existing theme classes
  clearThemeClasses(root);

  // Apply variant class (skip default variant)
  if (config.variant !== "default") {
    root.classList.add(`theme-${config.variant}`);
  }

  // Apply mode class
  root.classList.add(shouldUseDarkMode(config) ? "dark" : "light");

  // Re-enable transitions after first application
  if (root.classList.contains("no-transition")) {
    requestAnimationFrame(() => {
      root.classList.remove("no-transition");
    });
  }
}
